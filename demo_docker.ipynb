{"cells": [{"cell_type": "markdown", "id": "docker-intro", "metadata": {}, "source": ["# OrienterNet Docker Demo\n", "\n", "🐳 **Running in Docker Container**\n", "\n", "This notebook demonstrates OrienterNet's visual localization capabilities using 2D maps.\n", "\n", "**What this demo does:**\n", "- Takes an image as input\n", "- Finds its exact location on OpenStreetMap\n", "- Shows the localization process step by step\n", "\n", "**Note:** The first run will download the pretrained model automatically (may take a few minutes)."]}, {"cell_type": "code", "execution_count": 1, "id": "setup", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.9/site-packages/timm/models/layers/__init__.py:48: FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers\n", "  warnings.warn(f\"Importing from {__name__} is deprecated, please import via timm.layers\", FutureWarning)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ All imports successful!\n", "🚀 Initializing OrienterNet demo...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-07-07 07:23:55 maploc WARNING] perspective2d not available, using fallback calibrator\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Demo initialized successfully!\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib\n", "matplotlib.use('Agg')  # Use non-GUI backend for Docker\n", "\n", "# Import OrienterNet components\n", "try:\n", "    from maploc.demo import Demo\n", "    from maploc.osm.viz import GeoPlotter\n", "    from maploc.osm.tiling import TileManager\n", "    from maploc.osm.viz import Colormap, plot_nodes\n", "    from maploc.utils.viz_2d import plot_images\n", "    print(\"✅ All imports successful!\")\n", "except ImportError as e:\n", "    print(f\"❌ Import error: {e}\")\n", "    print(\"This might be due to missing dependencies. Check the Docker build logs.\")\n", "\n", "# Initialize the demo\n", "# Increasing num_rotations increases accuracy but requires more memory\n", "# num_rotations=64~128 is often sufficient for testing\n", "print(\"🚀 Initializing OrienterNet demo...\")\n", "demo = Demo(num_rotations=128, device=\"cpu\")  # Using CPU for Docker compatibility\n", "print(\"✅ Demo initialized successfully!\")"]}, {"cell_type": "markdown", "id": "example-1", "metadata": {}, "source": ["## Example 1: Zurich Image\n", "\n", "Let's start with a sample image from Zurich, Switzerland."]}, {"cell_type": "code", "execution_count": 2, "id": "zurich-demo", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-07-07 07:24:15 maploc INFO] Using (roll, pitch) (0.0, 0.0).\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📸 Processing image: assets/query_zurich_1.JPG\n", "📍 Prior location: ETH CAB Zurich\n", "🗺️ Search radius: 128 meters\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-07-07 07:24:16 maploc INFO] Using prior address 'ETH CAB, 6, Universitätstrasse, Oberstrass, Kreis 6, Zürich, Bezirk Zürich, Zürich, 8092, Schweiz/Suisse/Svizzera/Svizra'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Input image processed successfully!\n"]}], "source": ["# Process the Zurich example image\n", "image_path = \"assets/query_zurich_1.JPG\"\n", "prior_address = \"ETH CAB Zurich\"  # Optional: helps narrow down the search area\n", "tile_size_meters = 128  # Size of the map tile to search in\n", "\n", "print(f\"📸 Processing image: {image_path}\")\n", "print(f\"📍 Prior location: {prior_address}\")\n", "print(f\"🗺️ Search radius: {tile_size_meters} meters\")\n", "\n", "# Read and process the input image\n", "image, camera, gravity, proj, bbox = demo.read_input_image(\n", "    image_path,\n", "    prior_address=prior_address,\n", "    tile_size_meters=tile_size_meters,\n", ")\n", "\n", "print(\"✅ Input image processed successfully!\")"]}, {"cell_type": "code", "execution_count": 3, "id": "get-map", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-07-07 07:24:21 maploc INFO] Calling the OpenStreetMap API...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🗺️ Downloading map data from OpenStreetMap...\n", "✅ Map data retrieved successfully!\n"]}], "source": ["# Get the map data for the area\n", "print(\"🗺️ Downloading map data from OpenStreetMap...\")\n", "\n", "tiler = TileManager.from_bbox(proj, bbox + 10, demo.config.data.pixel_per_meter)\n", "canvas = tiler.query(bbox)\n", "map_viz = Colormap.apply(canvas.raster)\n", "\n", "print(\"✅ Map data retrieved successfully!\")"]}, {"cell_type": "code", "execution_count": 4, "id": "show-inputs", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Creating input visualization...\n", "✅ Input visualization complete!\n"]}], "source": ["# Visualize the input image and map\n", "print(\"📊 Creating input visualization...\")\n", "\n", "plt.figure(figsize=(15, 6))\n", "plot_images([image, map_viz], titles=[\"Input Image\", \"OpenStreetMap Raster\"], pad=2)\n", "plot_nodes(1, canvas.raster[2], fontsize=6, size=10)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Input visualization complete!\")"]}, {"cell_type": "code", "execution_count": 5, "id": "run-localization", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 Running OrienterNet localization...\n", "This may take a minute or two...\n", "✅ Localization completed successfully!\n", "📍 Predicted position: tensor([140., 298.])\n", "🧭 Predicted heading: 157.50°\n"]}], "source": ["# Run the localization\n", "print(\"🎯 Running OrienterNet localization...\")\n", "print(\"This may take a minute or two...\")\n", "\n", "uv, yaw, prob, neural_map, image_rectified = demo.localize(\n", "    image, camera, canvas, gravity=gravity\n", ")\n", "\n", "print(\"✅ Localization completed successfully!\")\n", "print(f\"📍 Predicted position: {uv}\")\n", "print(f\"🧭 Predicted heading: {yaw:.2f}°\")"]}, {"cell_type": "code", "execution_count": 6, "id": "show-results", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Creating result visualization...\n", "✅ Result visualization complete!\n"]}], "source": ["# Visualize the results\n", "from maploc.utils.viz_localization import (\n", "    add_circle_inset,\n", "    likelihood_overlay,\n", "    plot_dense_rotations,\n", ")\n", "from maploc.utils.viz_2d import features_to_RGB\n", "\n", "print(\"📊 Creating result visualization...\")\n", "\n", "# Create heatmap and neural map visualization\n", "overlay = likelihood_overlay(prob.numpy().max(-1), map_viz.mean(-1, keepdims=True))\n", "(neural_map_rgb,) = features_to_RGB(neural_map.numpy())\n", "\n", "plt.figure(figsize=(15, 6))\n", "plot_images([overlay, neural_map_rgb], titles=[\"Localization Heatmap\", \"Neural Map Features\"], pad=2)\n", "ax = plt.gcf().axes[0]\n", "ax.scatter(*canvas.to_uv(bbox.center), s=5, c=\"red\", label=\"Search center\")\n", "plot_dense_rotations(ax, prob, w=0.005, s=1 / 25)\n", "add_circle_inset(ax, uv)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Result visualization complete!\")"]}, {"cell_type": "code", "execution_count": 7, "id": "show-coordinates", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎉 Final Results:\n", "==================================================\n", "📍 Latitude, Longitude: 47.378152, 8.548010\n", "🧭 Heading angle: 157.50°\n", "🗺️ Google Maps link: https://www.google.com/maps/@47.37815216288451,8.548009978805375,18z\n", "==================================================\n"]}], "source": ["# Show the final coordinates\n", "latlon = proj.unproject(canvas.to_xy(uv))\n", "\n", "print(\"🎉 Final Results:\")\n", "print(\"=\" * 50)\n", "print(f\"📍 Latitude, Longitude: {latlon[0]:.6f}, {latlon[1]:.6f}\")\n", "print(f\"🧭 Heading angle: {yaw:.2f}°\")\n", "print(f\"🗺️ Google Maps link: https://www.google.com/maps/@{latlon[0]},{latlon[1]},18z\")\n", "print(\"=\" * 50)"]}, {"cell_type": "markdown", "id": "try-more", "metadata": {}, "source": ["## Try More Examples!\n", "\n", "You can try other example images or upload your own:"]}, {"cell_type": "code", "execution_count": null, "id": "vancouver-example", "metadata": {}, "outputs": [], "source": ["# Try Vancouver example\n", "print(\"🍁 Trying Vancouver example...\")\n", "\n", "image_path_vancouver = \"assets/query_vancouver_1.JPG\"\n", "prior_address_vancouver = \"Vancouver Waterfront Station\"\n", "\n", "# Process Vancouver image\n", "image_van, camera_van, gravity_van, proj_van, bbox_van = demo.read_input_image(\n", "    image_path_vancouver,\n", "    prior_address=prior_address_vancouver,\n", "    tile_size_meters=128,\n", ")\n", "\n", "# Get map data\n", "tiler_van = TileManager.from_bbox(proj_van, bbox_van + 10, demo.config.data.pixel_per_meter)\n", "canvas_van = tiler_van.query(bbox_van)\n", "map_viz_van = Colormap.apply(canvas_van.raster)\n", "\n", "# Show input\n", "plt.figure(figsize=(15, 6))\n", "plot_images([image_van, map_viz_van], titles=[\"Vancouver Image\", \"Vancouver Map\"], pad=2)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Vancouver example loaded! Run the next cell to localize.\")"]}, {"cell_type": "code", "execution_count": null, "id": "vancouver-localize", "metadata": {}, "outputs": [], "source": ["# Localize Vancouver image\n", "print(\"🎯 Localizing Vancouver image...\")\n", "\n", "uv_van, yaw_van, prob_van, neural_map_van, image_rectified_van = demo.localize(\n", "    image_van, camera_van, canvas_van, gravity=gravity_van\n", ")\n", "\n", "# Show results\n", "latlon_van = proj_van.unproject(canvas_van.to_xy(uv_van))\n", "\n", "print(\"🎉 Vancouver Results:\")\n", "print(\"=\" * 50)\n", "print(f\"📍 Latitude, Longitude: {latlon_van[0]:.6f}, {latlon_van[1]:.6f}\")\n", "print(f\"🧭 Heading angle: {yaw_van:.2f}°\")\n", "print(f\"🗺️ Google Maps link: https://www.google.com/maps/@{latlon_van[0]},{latlon_van[1]},18z\")\n", "print(\"=\" * 50)"]}, {"cell_type": "markdown", "id": "upload-own", "metadata": {}, "source": ["## Upload Your Own Image\n", "\n", "To test with your own images:\n", "\n", "1. **Upload your image** to the `/app/uploads/` directory (you can use the Jupyter file browser)\n", "2. **Modify the code below** with your image path and location hint\n", "3. **Run the cells** to see the localization results\n", "\n", "**Tips:**\n", "- Images with GPS EXIF data work best\n", "- Provide a rough location hint if GPS data is missing\n", "- Urban areas with distinctive buildings work better than rural areas"]}, {"cell_type": "code", "execution_count": null, "id": "custom-image", "metadata": {}, "outputs": [], "source": ["# Template for your own image\n", "# Uncomment and modify the lines below:\n", "\n", "# your_image_path = \"/app/uploads/your_image.jpg\"  # Replace with your image path\n", "# your_location_hint = \"Your City, Country\"        # Replace with location hint\n", "\n", "# # Process your image\n", "# image_custom, camera_custom, gravity_custom, proj_custom, bbox_custom = demo.read_input_image(\n", "#     your_image_path,\n", "#     prior_address=your_location_hint,\n", "#     tile_size_meters=256,  # Increase if location hint is very rough\n", "# )\n", "\n", "# # Get map and localize\n", "# tiler_custom = TileManager.from_bbox(proj_custom, bbox_custom + 10, demo.config.data.pixel_per_meter)\n", "# canvas_custom = tiler_custom.query(bbox_custom)\n", "# uv_custom, yaw_custom, prob_custom, neural_map_custom, _ = demo.localize(\n", "#     image_custom, camera_custom, canvas_custom, gravity=gravity_custom\n", "# )\n", "\n", "# # Show results\n", "# latlon_custom = proj_custom.unproject(canvas_custom.to_xy(uv_custom))\n", "# print(f\"📍 Your image location: {latlon_custom[0]:.6f}, {latlon_custom[1]:.6f}\")\n", "# print(f\"🧭 Heading: {yaw_custom:.2f}°\")\n", "\n", "print(\"💡 Uncomment and modify the code above to test your own images!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}