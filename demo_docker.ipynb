{"cells": [{"cell_type": "markdown", "id": "docker-intro", "metadata": {}, "source": ["# OrienterNet Docker Demo\n", "\n", "🐳 **Running in Docker Container**\n", "\n", "This notebook demonstrates OrienterNet's visual localization capabilities using 2D maps.\n", "\n", "**What this demo does:**\n", "- Takes an image as input\n", "- Finds its exact location on OpenStreetMap\n", "- Shows the localization process step by step\n", "\n", "**Note:** The first run will download the pretrained model automatically (may take a few minutes)."]}, {"cell_type": "code", "execution_count": 1, "id": "setup", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-07-07 06:42:35 maploc INFO] Downloading https://cvg-data.inf.ethz.ch/OrienterNet_CVPR2023/orienternet_mgl.ckpt to /app/experiments/orienternet_mgl.ckpt.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ All imports successful!\n", "🚀 Initializing OrienterNet demo...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8400de0902264466bbaa759cebe1cba3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/660434743 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "UnpicklingError", "evalue": "Weights only load failed. This file can still be loaded, to do so you have two options, \u001b[1mdo those steps only if you trust the source of the checkpoint\u001b[0m. \n\t(1) In PyTorch 2.6, we changed the default value of the `weights_only` argument in `torch.load` from `False` to `True`. Re-running `torch.load` with `weights_only` set to `False` will likely succeed, but it can result in arbitrary code execution. Do it only if you got the file from a trusted source.\n\t(2) Alternatively, to load with `weights_only=True` please check the recommended steps in the following error message.\n\tWeightsUnpickler error: Unsupported global: GLOBAL omegaconf.dictconfig.DictConfig was not an allowed global by default. Please use `torch.serialization.add_safe_globals([omegaconf.dictconfig.DictConfig])` or the `torch.serialization.safe_globals([omegaconf.dictconfig.DictConfig])` context manager to allowlist this global if you trust this class/function.\n\nCheck the documentation of torch.load to learn more about types accepted by default with weights_only https://pytorch.org/docs/stable/generated/torch.load.html.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mUnpicklingError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 21\u001b[0m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;66;03m# Initialize the demo\u001b[39;00m\n\u001b[1;32m     18\u001b[0m \u001b[38;5;66;03m# Increasing num_rotations increases accuracy but requires more memory\u001b[39;00m\n\u001b[1;32m     19\u001b[0m \u001b[38;5;66;03m# num_rotations=64~128 is often sufficient for testing\u001b[39;00m\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m🚀 Initializing OrienterNet demo...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 21\u001b[0m demo \u001b[38;5;241m=\u001b[39m \u001b[43mDemo\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnum_rotations\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m128\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdevice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcpu\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# Using CPU for Docker compatibility\u001b[39;00m\n\u001b[1;32m     22\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m✅ Demo initialized successfully!\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/app/maploc/demo.py:118\u001b[0m, in \u001b[0;36mDemo.__init__\u001b[0;34m(self, experiment_or_path, device, **kwargs)\u001b[0m\n\u001b[1;32m    116\u001b[0m     experiment_or_path, _ \u001b[38;5;241m=\u001b[39m pretrained_models[experiment_or_path]\n\u001b[1;32m    117\u001b[0m path \u001b[38;5;241m=\u001b[39m resolve_checkpoint_path(experiment_or_path)\n\u001b[0;32m--> 118\u001b[0m ckpt \u001b[38;5;241m=\u001b[39m \u001b[43mtorch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmap_location\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstorage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mloc\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstorage\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    119\u001b[0m config \u001b[38;5;241m=\u001b[39m ckpt[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhyper_parameters\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m    120\u001b[0m config\u001b[38;5;241m.\u001b[39mmodel\u001b[38;5;241m.\u001b[39mupdate(kwargs)\n", "File \u001b[0;32m/usr/local/lib/python3.9/site-packages/torch/serialization.py:1524\u001b[0m, in \u001b[0;36mload\u001b[0;34m(f, map_location, pickle_module, weights_only, mmap, **pickle_load_args)\u001b[0m\n\u001b[1;32m   1516\u001b[0m                 \u001b[38;5;28;01mreturn\u001b[39;00m _load(\n\u001b[1;32m   1517\u001b[0m                     opened_zipfile,\n\u001b[1;32m   1518\u001b[0m                     map_location,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1521\u001b[0m                     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mpickle_load_args,\n\u001b[1;32m   1522\u001b[0m                 )\n\u001b[1;32m   1523\u001b[0m             \u001b[38;5;28;01mexcept\u001b[39;00m pickle\u001b[38;5;241m.\u001b[39mUnpicklingError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m-> 1524\u001b[0m                 \u001b[38;5;28;01mraise\u001b[39;00m pickle\u001b[38;5;241m.\u001b[39mUnpicklingError(_get_wo_message(\u001b[38;5;28mstr\u001b[39m(e))) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[1;32m   1525\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m _load(\n\u001b[1;32m   1526\u001b[0m             opened_zipfile,\n\u001b[1;32m   1527\u001b[0m             map_location,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1530\u001b[0m             \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mpickle_load_args,\n\u001b[1;32m   1531\u001b[0m         )\n\u001b[1;32m   1532\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m mmap:\n", "\u001b[0;31mUnpicklingError\u001b[0m: Weights only load failed. This file can still be loaded, to do so you have two options, \u001b[1mdo those steps only if you trust the source of the checkpoint\u001b[0m. \n\t(1) In PyTorch 2.6, we changed the default value of the `weights_only` argument in `torch.load` from `False` to `True`. Re-running `torch.load` with `weights_only` set to `False` will likely succeed, but it can result in arbitrary code execution. Do it only if you got the file from a trusted source.\n\t(2) Alternatively, to load with `weights_only=True` please check the recommended steps in the following error message.\n\tWeightsUnpickler error: Unsupported global: GLOBAL omegaconf.dictconfig.DictConfig was not an allowed global by default. Please use `torch.serialization.add_safe_globals([omegaconf.dictconfig.DictConfig])` or the `torch.serialization.safe_globals([omegaconf.dictconfig.DictConfig])` context manager to allowlist this global if you trust this class/function.\n\nCheck the documentation of torch.load to learn more about types accepted by default with weights_only https://pytorch.org/docs/stable/generated/torch.load.html."]}], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib\n", "matplotlib.use('Agg')  # Use non-GUI backend for Docker\n", "\n", "# Import OrienterNet components\n", "try:\n", "    from maploc.demo import Demo\n", "    from maploc.osm.viz import GeoPlotter\n", "    from maploc.osm.tiling import TileManager\n", "    from maploc.osm.viz import Colormap, plot_nodes\n", "    from maploc.utils.viz_2d import plot_images\n", "    print(\"✅ All imports successful!\")\n", "except ImportError as e:\n", "    print(f\"❌ Import error: {e}\")\n", "    print(\"This might be due to missing dependencies. Check the Docker build logs.\")\n", "\n", "# Initialize the demo\n", "# Increasing num_rotations increases accuracy but requires more memory\n", "# num_rotations=64~128 is often sufficient for testing\n", "print(\"🚀 Initializing OrienterNet demo...\")\n", "demo = Demo(num_rotations=128, device=\"cpu\")  # Using CPU for Docker compatibility\n", "print(\"✅ Demo initialized successfully!\")"]}, {"cell_type": "markdown", "id": "example-1", "metadata": {}, "source": ["## Example 1: Zurich Image\n", "\n", "Let's start with a sample image from Zurich, Switzerland."]}, {"cell_type": "code", "execution_count": null, "id": "zurich-demo", "metadata": {}, "outputs": [], "source": ["# Process the Zurich example image\n", "image_path = \"assets/query_zurich_1.JPG\"\n", "prior_address = \"ETH CAB Zurich\"  # Optional: helps narrow down the search area\n", "tile_size_meters = 128  # Size of the map tile to search in\n", "\n", "print(f\"📸 Processing image: {image_path}\")\n", "print(f\"📍 Prior location: {prior_address}\")\n", "print(f\"🗺️ Search radius: {tile_size_meters} meters\")\n", "\n", "# Read and process the input image\n", "image, camera, gravity, proj, bbox = demo.read_input_image(\n", "    image_path,\n", "    prior_address=prior_address,\n", "    tile_size_meters=tile_size_meters,\n", ")\n", "\n", "print(\"✅ Input image processed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "id": "get-map", "metadata": {}, "outputs": [], "source": ["# Get the map data for the area\n", "print(\"🗺️ Downloading map data from OpenStreetMap...\")\n", "\n", "tiler = TileManager.from_bbox(proj, bbox + 10, demo.config.data.pixel_per_meter)\n", "canvas = tiler.query(bbox)\n", "map_viz = Colormap.apply(canvas.raster)\n", "\n", "print(\"✅ Map data retrieved successfully!\")"]}, {"cell_type": "code", "execution_count": null, "id": "show-inputs", "metadata": {}, "outputs": [], "source": ["# Visualize the input image and map\n", "print(\"📊 Creating input visualization...\")\n", "\n", "plt.figure(figsize=(15, 6))\n", "plot_images([image, map_viz], titles=[\"Input Image\", \"OpenStreetMap Raster\"], pad=2)\n", "plot_nodes(1, canvas.raster[2], fontsize=6, size=10)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Input visualization complete!\")"]}, {"cell_type": "code", "execution_count": null, "id": "run-localization", "metadata": {}, "outputs": [], "source": ["# Run the localization\n", "print(\"🎯 Running OrienterNet localization...\")\n", "print(\"This may take a minute or two...\")\n", "\n", "uv, yaw, prob, neural_map, image_rectified = demo.localize(\n", "    image, camera, canvas, gravity=gravity\n", ")\n", "\n", "print(\"✅ Localization completed successfully!\")\n", "print(f\"📍 Predicted position: {uv}\")\n", "print(f\"🧭 Predicted heading: {yaw:.2f}°\")"]}, {"cell_type": "code", "execution_count": null, "id": "show-results", "metadata": {}, "outputs": [], "source": ["# Visualize the results\n", "from maploc.utils.viz_localization import (\n", "    add_circle_inset,\n", "    likelihood_overlay,\n", "    plot_dense_rotations,\n", ")\n", "from maploc.utils.viz_2d import features_to_RGB\n", "\n", "print(\"📊 Creating result visualization...\")\n", "\n", "# Create heatmap and neural map visualization\n", "overlay = likelihood_overlay(prob.numpy().max(-1), map_viz.mean(-1, keepdims=True))\n", "(neural_map_rgb,) = features_to_RGB(neural_map.numpy())\n", "\n", "plt.figure(figsize=(15, 6))\n", "plot_images([overlay, neural_map_rgb], titles=[\"Localization Heatmap\", \"Neural Map Features\"], pad=2)\n", "ax = plt.gcf().axes[0]\n", "ax.scatter(*canvas.to_uv(bbox.center), s=5, c=\"red\", label=\"Search center\")\n", "plot_dense_rotations(ax, prob, w=0.005, s=1 / 25)\n", "add_circle_inset(ax, uv)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Result visualization complete!\")"]}, {"cell_type": "code", "execution_count": null, "id": "show-coordinates", "metadata": {}, "outputs": [], "source": ["# Show the final coordinates\n", "latlon = proj.unproject(canvas.to_xy(uv))\n", "\n", "print(\"🎉 Final Results:\")\n", "print(\"=\" * 50)\n", "print(f\"📍 Latitude, Longitude: {latlon[0]:.6f}, {latlon[1]:.6f}\")\n", "print(f\"🧭 Heading angle: {yaw:.2f}°\")\n", "print(f\"🗺️ Google Maps link: https://www.google.com/maps/@{latlon[0]},{latlon[1]},18z\")\n", "print(\"=\" * 50)"]}, {"cell_type": "markdown", "id": "try-more", "metadata": {}, "source": ["## Try More Examples!\n", "\n", "You can try other example images or upload your own:"]}, {"cell_type": "code", "execution_count": null, "id": "vancouver-example", "metadata": {}, "outputs": [], "source": ["# Try Vancouver example\n", "print(\"🍁 Trying Vancouver example...\")\n", "\n", "image_path_vancouver = \"assets/query_vancouver_1.JPG\"\n", "prior_address_vancouver = \"Vancouver Waterfront Station\"\n", "\n", "# Process Vancouver image\n", "image_van, camera_van, gravity_van, proj_van, bbox_van = demo.read_input_image(\n", "    image_path_vancouver,\n", "    prior_address=prior_address_vancouver,\n", "    tile_size_meters=128,\n", ")\n", "\n", "# Get map data\n", "tiler_van = TileManager.from_bbox(proj_van, bbox_van + 10, demo.config.data.pixel_per_meter)\n", "canvas_van = tiler_van.query(bbox_van)\n", "map_viz_van = Colormap.apply(canvas_van.raster)\n", "\n", "# Show input\n", "plt.figure(figsize=(15, 6))\n", "plot_images([image_van, map_viz_van], titles=[\"Vancouver Image\", \"Vancouver Map\"], pad=2)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Vancouver example loaded! Run the next cell to localize.\")"]}, {"cell_type": "code", "execution_count": null, "id": "vancouver-localize", "metadata": {}, "outputs": [], "source": ["# Localize Vancouver image\n", "print(\"🎯 Localizing Vancouver image...\")\n", "\n", "uv_van, yaw_van, prob_van, neural_map_van, image_rectified_van = demo.localize(\n", "    image_van, camera_van, canvas_van, gravity=gravity_van\n", ")\n", "\n", "# Show results\n", "latlon_van = proj_van.unproject(canvas_van.to_xy(uv_van))\n", "\n", "print(\"🎉 Vancouver Results:\")\n", "print(\"=\" * 50)\n", "print(f\"📍 Latitude, Longitude: {latlon_van[0]:.6f}, {latlon_van[1]:.6f}\")\n", "print(f\"🧭 Heading angle: {yaw_van:.2f}°\")\n", "print(f\"🗺️ Google Maps link: https://www.google.com/maps/@{latlon_van[0]},{latlon_van[1]},18z\")\n", "print(\"=\" * 50)"]}, {"cell_type": "markdown", "id": "upload-own", "metadata": {}, "source": ["## Upload Your Own Image\n", "\n", "To test with your own images:\n", "\n", "1. **Upload your image** to the `/app/uploads/` directory (you can use the Jupyter file browser)\n", "2. **Modify the code below** with your image path and location hint\n", "3. **Run the cells** to see the localization results\n", "\n", "**Tips:**\n", "- Images with GPS EXIF data work best\n", "- Provide a rough location hint if GPS data is missing\n", "- Urban areas with distinctive buildings work better than rural areas"]}, {"cell_type": "code", "execution_count": null, "id": "custom-image", "metadata": {}, "outputs": [], "source": ["# Template for your own image\n", "# Uncomment and modify the lines below:\n", "\n", "# your_image_path = \"/app/uploads/your_image.jpg\"  # Replace with your image path\n", "# your_location_hint = \"Your City, Country\"        # Replace with location hint\n", "\n", "# # Process your image\n", "# image_custom, camera_custom, gravity_custom, proj_custom, bbox_custom = demo.read_input_image(\n", "#     your_image_path,\n", "#     prior_address=your_location_hint,\n", "#     tile_size_meters=256,  # Increase if location hint is very rough\n", "# )\n", "\n", "# # Get map and localize\n", "# tiler_custom = TileManager.from_bbox(proj_custom, bbox_custom + 10, demo.config.data.pixel_per_meter)\n", "# canvas_custom = tiler_custom.query(bbox_custom)\n", "# uv_custom, yaw_custom, prob_custom, neural_map_custom, _ = demo.localize(\n", "#     image_custom, camera_custom, canvas_custom, gravity=gravity_custom\n", "# )\n", "\n", "# # Show results\n", "# latlon_custom = proj_custom.unproject(canvas_custom.to_xy(uv_custom))\n", "# print(f\"📍 Your image location: {latlon_custom[0]:.6f}, {latlon_custom[1]:.6f}\")\n", "# print(f\"🧭 Heading: {yaw_custom:.2f}°\")\n", "\n", "print(\"💡 Uncomment and modify the code above to test your own images!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}