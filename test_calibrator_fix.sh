#!/bin/bash

# Test the ImageCalibrator fix
echo "🔧 Testing ImageCalibrator Fix"
echo "=============================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}[INFO]${NC} Fixed ImageCalibrator to work without perspective2d"
echo -e "${BLUE}[INFO]${NC} Added fallback .to() and .run() methods"
echo ""

# Stop any running containers
docker stop $(docker ps -q --filter ancestor=orienternet-jupyter) 2>/dev/null || true

# Remove old image
docker rmi orienternet-jupyter 2>/dev/null || true

echo -e "${YELLOW}[BUILD]${NC} Rebuilding with ImageCalibrator fix..."
if docker build -f Dockerfile.jupyter -t orienternet-jupyter . --no-cache; then
    echo -e "${GREEN}[SUCCESS]${NC} Build completed!"
    echo ""
    echo "🚀 Starting Jupyter Lab..."
    echo "📖 Open http://localhost:8888"
    echo "📓 Try demo_docker.ipynb"
    echo ""
    echo -e "${YELLOW}[NOTE]${NC} ImageCalibrator will use fallback mode (no perspective2d)"
    echo -e "${YELLOW}[NOTE]${NC} This may affect accuracy but demo should work"
    echo ""
    
    # Create directories
    mkdir -p experiments datasets
    
    docker run -it --rm \
        -p 8888:8888 \
        -v "$(pwd)/experiments:/app/experiments" \
        -v "$(pwd)/datasets:/app/datasets" \
        -v "$(pwd)/demo.ipynb:/app/demo.ipynb" \
        -v "$(pwd)/demo_docker.ipynb:/app/demo_docker.ipynb" \
        -v "$(pwd)/assets:/app/assets" \
        orienternet-jupyter
else
    echo -e "${RED}[ERROR]${NC} Build failed!"
    exit 1
fi
