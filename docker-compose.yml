version: '3.8'

services:
  orienternet-demo:
    build: .
    ports:
      - "7860:7860"
    volumes:
      # Mount for persistent model storage
      - ./experiments:/app/experiments
      - ./datasets:/app/datasets
      # Mount for uploading custom images
      - ./uploads:/app/uploads
    environment:
      - PYTHONPATH=/app
      - GRADIO_SERVER_NAME=0.0.0.0
      - GRADIO_SERVER_PORT=7860
    command: python app.py
    
  orienternet-jupyter:
    build: .
    ports:
      - "8888:8888"
    volumes:
      - ./experiments:/app/experiments
      - ./datasets:/app/datasets
      - ./notebooks:/app/notebooks
      - ./uploads:/app/uploads
    environment:
      - PYTHONPATH=/app
    command: jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token='' --NotebookApp.password=''
    profiles:
      - jupyter
