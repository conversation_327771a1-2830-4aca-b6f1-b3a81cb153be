#!/bin/bash

# OrienterNet Docker Demo Runner
# This script helps you build and run the OrienterNet demo in Docker

set -e

echo "🐳 OrienterNet Docker Demo Setup"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p experiments datasets uploads

# Function to build the Docker image
build_image() {
    print_status "Building OrienterNet Docker image..."
    docker build -t orienternet-demo .
    print_success "Docker image built successfully!"
}

# Function to run the Gradio demo
run_gradio_demo() {
    print_status "Starting OrienterNet Gradio demo..."
    print_warning "First run may take longer as the pretrained model downloads automatically."
    echo ""
    print_status "The demo will be available at: http://localhost:7860"
    echo ""
    
    docker run -it --rm \
        -p 7860:7860 \
        -v "$(pwd)/experiments:/app/experiments" \
        -v "$(pwd)/datasets:/app/datasets" \
        -v "$(pwd)/uploads:/app/uploads" \
        orienternet-demo python docker_demo.py
}

# Function to run Jupyter notebook
run_jupyter() {
    print_status "Starting OrienterNet Jupyter notebook..."
    echo ""
    print_status "Jupyter will be available at: http://localhost:8888"
    print_status "No password required - access directly via the URL above"
    echo ""
    
    docker run -it --rm \
        -p 8888:8888 \
        -v "$(pwd)/experiments:/app/experiments" \
        -v "$(pwd)/datasets:/app/datasets" \
        -v "$(pwd)/notebooks:/app/notebooks" \
        -v "$(pwd)/uploads:/app/uploads" \
        orienternet-demo \
        jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token='' --NotebookApp.password=''
}

# Function to run with Docker Compose
run_with_compose() {
    print_status "Starting with Docker Compose..."
    docker-compose up --build orienternet-demo
}

# Function to run Jupyter with Docker Compose
run_jupyter_compose() {
    print_status "Starting Jupyter with Docker Compose..."
    docker-compose --profile jupyter up --build orienternet-jupyter
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  build          Build the Docker image"
    echo "  demo           Run the Gradio web demo"
    echo "  jupyter        Run Jupyter notebook"
    echo "  compose        Run demo with Docker Compose"
    echo "  compose-jupyter Run Jupyter with Docker Compose"
    echo "  clean          Clean up Docker images and containers"
    echo "  help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build          # Build the image first"
    echo "  $0 demo           # Run the web demo"
    echo "  $0 jupyter        # Run Jupyter notebook"
    echo "  $0 compose        # Use Docker Compose"
}

# Function to clean up
cleanup() {
    print_status "Cleaning up Docker resources..."
    docker-compose down 2>/dev/null || true
    docker rmi orienternet-demo 2>/dev/null || true
    print_success "Cleanup completed!"
}

# Main script logic
case "${1:-demo}" in
    "build")
        build_image
        ;;
    "demo")
        build_image
        run_gradio_demo
        ;;
    "jupyter")
        build_image
        run_jupyter
        ;;
    "compose")
        run_with_compose
        ;;
    "compose-jupyter")
        run_jupyter_compose
        ;;
    "clean")
        cleanup
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac
