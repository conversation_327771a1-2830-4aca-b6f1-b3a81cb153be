#!/bin/bash

# Quick fix for PyTorch 2.6 weights_only issue
echo "🔧 Fixing PyTorch 2.6 weights_only issue..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}[INFO]${NC} The issue was caused by PyTorch 2.6 changing torch.load default to weights_only=True"
echo -e "${BLUE}[INFO]${NC} Fixed by adding weights_only=False to torch.load calls"
echo ""

# Stop any running containers
docker stop $(docker ps -q --filter ancestor=orienternet-jupyter) 2>/dev/null || true

# Remove old image to force rebuild
docker rmi orienternet-jupyter 2>/dev/null || true

echo -e "${YELLOW}[BUILD]${NC} Rebuilding Docker image with PyTorch fix..."
if docker build -f Dockerfile.jupyter -t orienternet-jupyter . --no-cache; then
    echo -e "${GREEN}[SUCCESS]${NC} Build completed with PyTorch fix!"
    echo ""
    echo "🚀 Starting Jupyter Lab..."
    echo "📖 Open http://localhost:8888"
    echo "📓 Try demo_docker.ipynb (recommended)"
    echo ""
    echo -e "${YELLOW}[NOTE]${NC} The torch.load issue should now be resolved"
    echo ""
    
    # Create directories
    mkdir -p experiments datasets
    
    docker run -it --rm \
        -p 8888:8888 \
        -v "$(pwd)/experiments:/app/experiments" \
        -v "$(pwd)/datasets:/app/datasets" \
        -v "$(pwd)/demo.ipynb:/app/demo.ipynb" \
        -v "$(pwd)/demo_docker.ipynb:/app/demo_docker.ipynb" \
        -v "$(pwd)/assets:/app/assets" \
        orienternet-jupyter
else
    echo -e "${RED}[ERROR]${NC} Build failed!"
    echo "Check the error messages above."
    exit 1
fi
