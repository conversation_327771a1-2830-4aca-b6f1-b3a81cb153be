# OrienterNet Docker Container
FROM python:3.9

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    build-essential \
    libgeos-dev \
    libproj-dev \
    libspatialindex-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements/docker.txt /app/requirements/docker.txt
COPY setup.py /app/setup.py

# Install Python dependencies (using Docker-specific requirements)
RUN pip install --no-cache-dir -r requirements/docker.txt

# Copy the entire project
COPY . /app/

# Install the package in development mode
RUN pip install -e .

# Create directories for models and data
RUN mkdir -p /app/experiments /app/datasets

# Expose port for Gradio app
EXPOSE 7860

# Set environment variables
ENV PYTHONPATH=/app
ENV GRADIO_SERVER_NAME=0.0.0.0
ENV GRADIO_SERVER_PORT=7860
# Disable GUI for matplotlib and OpenCV
ENV MPLBACKEND=Agg
ENV DISPLAY=

# Default command - run the Gradio demo
CMD ["python", "app.py"]
