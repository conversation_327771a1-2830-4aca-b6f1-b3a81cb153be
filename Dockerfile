# OrienterNet Docker Container - Ubuntu base for better OpenCV support
FROM ubuntu:20.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    git \
    wget \
    curl \
    build-essential \
    libgeos-dev \
    libproj-dev \
    libspatialindex-dev \
    # OpenCV dependencies
    libopencv-dev \
    python3-opencv \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgthread-2.0-0 \
    # Virtual display
    xvfb \
    && rm -rf /var/lib/apt/lists/*

# Create symlinks for python
RUN ln -s /usr/bin/python3 /usr/bin/python
RUN ln -s /usr/bin/pip3 /usr/bin/pip

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements/docker.txt /app/requirements/docker.txt
COPY setup.py /app/setup.py

# Upgrade pip first
RUN pip install --upgrade pip

# Install Python dependencies (using Docker-specific requirements)
RUN pip install --no-cache-dir -r requirements/docker.txt

# Install perspective2d separately with error handling
RUN pip install --no-cache-dir git+https://github.com/jinlinyi/PerspectiveFields.git || echo "perspective2d installation failed, continuing..."

# Copy the entire project
COPY . /app/

# Install the package in development mode
RUN pip install -e .

# Create directories for models and data
RUN mkdir -p /app/experiments /app/datasets

# Expose port for Gradio app
EXPOSE 7860

# Set environment variables
ENV PYTHONPATH=/app
ENV GRADIO_SERVER_NAME=0.0.0.0
ENV GRADIO_SERVER_PORT=7860
# Disable GUI for matplotlib and OpenCV
ENV MPLBACKEND=Agg
ENV QT_QPA_PLATFORM=offscreen
ENV DISPLAY=:99

# Create startup script that handles virtual display
RUN echo '#!/bin/bash\n\
# Start virtual display for GUI applications\n\
Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &\n\
# Wait a moment for Xvfb to start\n\
sleep 2\n\
# Run the actual command\n\
exec "$@"' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# Use entrypoint script
ENTRYPOINT ["/app/entrypoint.sh"]

# Default command - run the Docker demo
CMD ["python", "docker_demo.py"]
