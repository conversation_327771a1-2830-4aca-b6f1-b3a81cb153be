# OrienterNet Docker Container
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    build-essential \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgeos-dev \
    libproj-dev \
    libspatialindex-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements/demo.txt /app/requirements/demo.txt
COPY setup.py /app/setup.py

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements/demo.txt
RUN pip install --no-cache-dir gradio

# Copy the entire project
COPY . /app/

# Install the package in development mode
RUN pip install -e .

# Create directories for models and data
RUN mkdir -p /app/experiments /app/datasets

# Expose port for Gradio app
EXPOSE 7860

# Set environment variables
ENV PYTHONPATH=/app
ENV GRADIO_SERVER_NAME=0.0.0.0
ENV GRADIO_SERVER_PORT=7860

# Default command - run the Gradio demo
CMD ["python", "app.py"]
