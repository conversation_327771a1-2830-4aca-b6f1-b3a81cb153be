name: <PERSON><PERSON> and Lint
on:
  push:
    branches:
      - main
    paths:
      - '*.py'
      - '*.ipynb'
  pull_request:
    types: [ assigned, opened, synchronize, reopened ]
jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.10'
          cache: 'pip'
          cache-dependency-path: 'requirements/dev.txt'
      - run: python -m pip install --upgrade pip
      - run: python -m pip install -r requirements/dev.txt
      - run: python -m flake8 maploc
      - run: python -m isort maploc *.ipynb --check-only --diff
      - run: python -m black maploc *.ipynb --check --diff
