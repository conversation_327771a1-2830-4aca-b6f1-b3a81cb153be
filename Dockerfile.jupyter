# OrienterNet Jupyter Docker Container
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    build-essential \
    libgeos-dev \
    libproj-dev \
    libspatialindex-dev \
    # Minimal OpenCV dependencies
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    # For Jupyter
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# Python is already available in python:3.9-slim

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements/docker.txt /app/requirements/docker.txt
COPY setup.py /app/setup.py

# Upgrade pip first
RUN pip install --upgrade pip

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements/docker.txt

# Install additional Jupyter extensions
RUN pip install --no-cache-dir \
    jupyterlab \
    ipywidgets \
    plotly \
    kaleido \
    opencv-python-headless

# Install perspective2d with error handling (optional)
RUN pip install --no-cache-dir git+https://github.com/jinlinyi/PerspectiveFields.git || echo "perspective2d installation failed, continuing without it..."

# Copy the entire project
COPY . /app/

# Install the package in development mode
RUN pip install -e .

# Create directories for models and data
RUN mkdir -p /app/experiments /app/datasets /app/notebooks

# Set environment variables
ENV PYTHONPATH=/app
# Disable GUI for matplotlib
ENV MPLBACKEND=Agg
ENV QT_QPA_PLATFORM=offscreen

# Expose port for Jupyter
EXPOSE 8888

# Create Jupyter config
RUN jupyter lab --generate-config

# Configure Jupyter to allow all IPs and disable authentication for demo
RUN echo "c.ServerApp.ip = '0.0.0.0'" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.port = 8888" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.token = ''" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.password = ''" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.open_browser = False" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.allow_root = True" >> ~/.jupyter/jupyter_lab_config.py

# Default command - run Jupyter Lab
CMD ["jupyter", "lab", "--ip=0.0.0.0", "--port=8888", "--no-browser", "--allow-root", "--NotebookApp.token=''", "--NotebookApp.password=''"]
