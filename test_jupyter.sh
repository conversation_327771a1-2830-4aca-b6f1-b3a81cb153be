#!/bin/bash

# Quick test script for <PERSON><PERSON>ter demo
echo "🧪 Quick Jupyter Test (Fixed)"
echo "============================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}[INFO]${NC} Building and starting Jupyter Lab..."
echo -e "${BLUE}[INFO]${NC} This will:"
echo "  1. Build the Docker image (fixed dependencies)"
echo "  2. Start Jupyter Lab on http://localhost:8888"
echo "  3. Make demo.ipynb available"
echo ""

# Create necessary directories
mkdir -p experiments datasets

# Build and run with better error handling
echo -e "${YELLOW}[BUILD]${NC} Building Docker image..."
if docker build -f Dockerfile.jupyter -t orienternet-jupyter . --no-cache; then
    echo -e "${GREEN}[SUCCESS]${NC} Build completed!"
    echo ""
    echo "🚀 Starting Jupyter Lab..."
    echo "📖 Open http://localhost:8888 in your browser"
    echo "📓 Available notebooks:"
    echo "   - demo.ipynb (original)"
    echo "   - demo_docker.ipynb (Docker optimized)"
    echo ""
    echo -e "${YELLOW}[NOTE]${NC} First run will download pretrained model (~100MB)"
    echo -e "${YELLOW}[NOTE]${NC} Press Ctrl+C to stop"
    echo ""

    docker run -it --rm \
        -p 8888:8888 \
        -v "$(pwd)/experiments:/app/experiments" \
        -v "$(pwd)/datasets:/app/datasets" \
        -v "$(pwd)/demo.ipynb:/app/demo.ipynb" \
        -v "$(pwd)/demo_docker.ipynb:/app/demo_docker.ipynb" \
        -v "$(pwd)/assets:/app/assets" \
        orienternet-jupyter
else
    echo -e "${RED}[ERROR]${NC} Build failed!"
    echo "Check the error messages above."
    exit 1
fi
