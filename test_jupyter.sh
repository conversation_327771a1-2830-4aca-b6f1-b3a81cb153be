#!/bin/bash

# Quick test script for Ju<PERSON>ter demo
echo "🧪 Quick Jupyter Test"
echo "===================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}[INFO]${NC} Building and starting Jupyter Lab..."
echo -e "${BLUE}[INFO]${NC} This will:"
echo "  1. Build the Docker image"
echo "  2. Start Jupyter Lab on http://localhost:8888"
echo "  3. Make demo.ipynb available"
echo ""

# Build and run
docker build -f Dockerfile.jupyter -t orienternet-jupyter . && \
echo -e "${GREEN}[SUCCESS]${NC} Build completed!" && \
echo "" && \
echo "🚀 Starting Jupyter Lab..." && \
echo "📖 Open http://localhost:8888 in your browser" && \
echo "📓 Click on demo.ipynb to start the demo" && \
echo "" && \
docker run -it --rm \
    -p 8888:8888 \
    -v "$(pwd)/experiments:/app/experiments" \
    -v "$(pwd)/datasets:/app/datasets" \
    -v "$(pwd)/demo.ipynb:/app/demo.ipynb" \
    -v "$(pwd)/assets:/app/assets" \
    orienternet-jupyter
