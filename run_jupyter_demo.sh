#!/bin/bash

# OrienterNet Jupyter Demo Runner
# This script helps you build and run the OrienterNet Jupyter demo in Docker

set -e

echo "📓 OrienterNet Jupyter Demo Setup"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p experiments datasets notebooks

# Function to build the Docker image
build_image() {
    print_status "Building OrienterNet Jupyter Docker image..."
    docker build -f Dockerfile.jupyter -t orienternet-jupyter .
    print_success "Docker image built successfully!"
}

# Function to run Jupyter Lab
run_jupyter() {
    print_status "Starting OrienterNet Jupyter Lab..."
    print_warning "First run may take longer as the pretrained model downloads automatically."
    echo ""
    print_status "Jupyter Lab will be available at: http://localhost:8888"
    print_status "No password required - access directly via the URL above"
    print_status "The demo.ipynb notebook will be available in the file browser"
    echo ""
    
    docker run -it --rm \
        -p 8888:8888 \
        -v "$(pwd)/experiments:/app/experiments" \
        -v "$(pwd)/datasets:/app/datasets" \
        -v "$(pwd)/notebooks:/app/notebooks" \
        -v "$(pwd)/demo.ipynb:/app/demo.ipynb" \
        -v "$(pwd)/assets:/app/assets" \
        orienternet-jupyter
}

# Function to run with Docker Compose
run_with_compose() {
    print_status "Starting with Docker Compose..."
    docker-compose -f docker-compose.jupyter.yml up --build
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  build          Build the Docker image"
    echo "  run            Run Jupyter Lab"
    echo "  compose        Run with Docker Compose"
    echo "  clean          Clean up Docker images and containers"
    echo "  help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build          # Build the image first"
    echo "  $0 run            # Run Jupyter Lab"
    echo "  $0 compose        # Use Docker Compose"
}

# Function to clean up
cleanup() {
    print_status "Cleaning up Docker resources..."
    docker-compose -f docker-compose.jupyter.yml down 2>/dev/null || true
    docker rmi orienternet-jupyter 2>/dev/null || true
    print_success "Cleanup completed!"
}

# Function to show instructions
show_instructions() {
    echo ""
    print_success "🎉 Jupyter Lab is starting!"
    echo ""
    echo "📋 Instructions:"
    echo "1. Open your browser and go to: http://localhost:8888"
    echo "2. You'll see the Jupyter Lab interface"
    echo "3. Click on 'demo.ipynb' to open the demo notebook"
    echo "4. Run the cells step by step to see the OrienterNet demo"
    echo ""
    echo "📁 Available files:"
    echo "- demo.ipynb: Main demo notebook"
    echo "- assets/: Example images (Zurich, Vancouver)"
    echo "- experiments/: Model storage (created automatically)"
    echo "- datasets/: Data storage"
    echo ""
    echo "💡 Tips:"
    echo "- The first cell will download the pretrained model (may take a few minutes)"
    echo "- You can modify the num_rotations parameter to balance speed vs accuracy"
    echo "- Try uploading your own images to test!"
    echo ""
    print_warning "Press Ctrl+C to stop the Jupyter server"
}

# Main script logic
case "${1:-run}" in
    "build")
        build_image
        ;;
    "run")
        build_image
        show_instructions
        run_jupyter
        ;;
    "compose")
        show_instructions
        run_with_compose
        ;;
    "clean")
        cleanup
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac
