#!/bin/bash

# Super simple Jupyter test - minimal dependencies
echo "🚀 Simple Jupyter Test"
echo "====================="

# Create a minimal Dockerfile on the fly
cat > Dockerfile.simple << 'EOF'
FROM python:3.9

# Install minimal system dependencies
RUN apt-get update && apt-get install -y \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements
COPY requirements/docker.txt /app/requirements/docker.txt
COPY setup.py /app/setup.py

# Install Python packages
RUN pip install --upgrade pip
RUN pip install --no-cache-dir -r requirements/docker.txt
RUN pip install --no-cache-dir \
    jupyterlab \
    ipywidgets \
    plotly \
    opencv-python-headless

# Copy project
COPY . /app/
RUN pip install -e .

# Jupyter config
RUN jupyter lab --generate-config
RUN echo "c.ServerApp.ip = '0.0.0.0'" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.port = 8888" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.token = ''" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.password = ''" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.open_browser = False" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.allow_root = True" >> ~/.jupyter/jupyter_lab_config.py

EXPOSE 8888
CMD ["jupyter", "lab", "--ip=0.0.0.0", "--port=8888", "--no-browser", "--allow-root"]
EOF

echo "📦 Building simple Docker image..."
docker build -f Dockerfile.simple -t orienternet-simple .

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo ""
    echo "🚀 Starting Jupyter Lab..."
    echo "📖 Open http://localhost:8888"
    echo "📓 Try demo_docker.ipynb (Docker optimized)"
    echo ""
    
    # Create directories
    mkdir -p experiments datasets
    
    docker run -it --rm \
        -p 8888:8888 \
        -v "$(pwd)/experiments:/app/experiments" \
        -v "$(pwd)/datasets:/app/datasets" \
        -v "$(pwd)/demo.ipynb:/app/demo.ipynb" \
        -v "$(pwd)/demo_docker.ipynb:/app/demo_docker.ipynb" \
        -v "$(pwd)/assets:/app/assets" \
        orienternet-simple
else
    echo "❌ Build failed!"
fi

# Clean up
rm -f Dockerfile.simple
