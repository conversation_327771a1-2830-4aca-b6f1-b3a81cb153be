FROM python:3.9

# Install minimal system dependencies
RUN apt-get update && apt-get install -y \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements
COPY requirements/docker.txt /app/requirements/docker.txt
COPY setup.py /app/setup.py

# Install Python packages
RUN pip install --upgrade pip
RUN pip install --no-cache-dir -r requirements/docker.txt
RUN pip install --no-cache-dir \
    jupyterlab \
    ipywidgets \
    plotly \
    opencv-python-headless

# Copy project
COPY . /app/
RUN pip install -e .

# Ju<PERSON>ter config
RUN jupyter lab --generate-config
RUN echo "c.ServerApp.ip = '0.0.0.0'" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.port = 8888" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.token = ''" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.password = ''" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.open_browser = False" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.allow_root = True" >> ~/.jupyter/jupyter_lab_config.py

EXPOSE 8888
CMD ["jupyter", "lab", "--ip=0.0.0.0", "--port=8888", "--no-browser", "--allow-root"]
