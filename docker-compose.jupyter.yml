version: '3.8'

services:
  orienternet-jupyter:
    build:
      context: .
      dockerfile: Dockerfile.jupyter
    ports:
      - "8888:8888"
    volumes:
      # Mount for persistent model storage
      - ./experiments:/app/experiments
      - ./datasets:/app/datasets
      # Mount notebooks directory
      - ./notebooks:/app/notebooks
      # Mount the demo notebook
      - ./demo.ipynb:/app/demo.ipynb
      # Mount assets for examples
      - ./assets:/app/assets
    environment:
      - PYTHONPATH=/app
      - MPLBACKEND=Agg
      - QT_QPA_PLATFORM=offscreen
    command: jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token='' --NotebookApp.password=''
