#!/bin/bash

# Quick fix for Docker OpenCV issues
echo "🔧 Fixing Docker OpenCV issues..."

# Stop any running containers
docker-compose down 2>/dev/null || true
docker stop $(docker ps -q --filter ancestor=orienternet-demo) 2>/dev/null || true

# Remove old image
docker rmi orienternet-demo 2>/dev/null || true

# Rebuild with fixed dependencies
echo "🐳 Rebuilding Docker image with fixes..."
docker build -t orienternet-demo .

echo "✅ Docker image rebuilt successfully!"
echo ""
echo "Now run the demo with:"
echo "./run_docker_demo.sh demo"
