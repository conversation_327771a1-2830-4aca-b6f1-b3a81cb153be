#!/bin/bash

# Quick fix for Docker OpenCV issues
echo "🔧 Fixing Docker OpenCV issues with Ubuntu base..."

# Stop any running containers
docker-compose down 2>/dev/null || true
docker stop $(docker ps -q --filter ancestor=orienternet-demo) 2>/dev/null || true

# Remove old image
docker rmi orienternet-demo 2>/dev/null || true

# Clean up any dangling images
docker image prune -f

# Rebuild with fixed dependencies
echo "🐳 Rebuilding Docker image with Ubuntu base and OpenCV fixes..."
echo "This may take a few minutes..."
docker build -t orienternet-demo . --no-cache

if [ $? -eq 0 ]; then
    echo "✅ Docker image rebuilt successfully!"
    echo ""
    echo "Now run the demo with:"
    echo "./run_docker_demo.sh demo"
    echo ""
    echo "Or test with the simplified version:"
    echo "docker run -it --rm -p 7860:7860 orienternet-demo python docker_demo_simple.py"
else
    echo "❌ Docker build failed!"
    echo "Check the error messages above."
fi
