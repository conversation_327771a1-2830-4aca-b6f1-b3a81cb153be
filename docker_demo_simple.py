#!/usr/bin/env python3
"""
Simplified Docker demo for OrienterNet without perspective2d dependency
"""

import csv
import sys
import os
import logging
from pathlib import Path

import gradio as gr
import matplotlib
matplotlib.use('Agg')  # Use non-GUI backend
import matplotlib.pyplot as plt

# Configure logging for Docker
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s %(name)s %(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Avoid CSV field size limit issues
csv.field_size_limit(sys.maxsize)
gr.utils.sanitize_value_for_csv = lambda v: v

# Create uploads directory if it doesn't exist
os.makedirs("/app/uploads", exist_ok=True)

def run(image, address, tile_size_meters, num_rotations):
    """Simplified demo function that bypasses perspective2d"""
    try:
        logger.info(f"Processing image: {image.name if image else 'None'}")
        logger.info(f"Address: {address}, Tile size: {tile_size_meters}m, Rotations: {num_rotations}")
        
        if image is None:
            raise gr.Error("Please upload an image first!")
            
        # Try to import Demo with error handling
        try:
            from maploc.demo import Demo
            logger.info("Successfully imported Demo")
        except ImportError as e:
            logger.error(f"Failed to import Demo: {e}")
            # Create a mock response for testing
            fig, ax = plt.subplots(1, 1, figsize=(10, 6))
            ax.text(0.5, 0.5, f"Demo import failed:\n{str(e)}\n\nThis is a test response.", 
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            ax.set_title("Import Error - Test Mode")
            return fig, fig, fig, f"Import error: {str(e)}"
        
        image_path = image.name
        demo = Demo(num_rotations=int(num_rotations), device="cpu")
        logger.info("Demo initialized successfully")

        # Read and process input
        try:
            image, camera, gravity, proj, bbox = demo.read_input_image(
                image_path,
                prior_address=address or None,
                tile_size_meters=int(tile_size_meters),
            )
            logger.info("Input image processed successfully")
        except ValueError as e:
            logger.error(f"Input processing error: {e}")
            raise gr.Error(f"Input processing failed: {str(e)}")

        # Get map data
        try:
            from maploc.osm.tiling import TileManager
            from maploc.osm.viz import Colormap, GeoPlotter, plot_nodes
            
            tiler = TileManager.from_bbox(proj, bbox + 10, demo.config.data.pixel_per_meter)
            canvas = tiler.query(bbox)
            map_viz = Colormap.apply(canvas.raster)
            logger.info("Map data retrieved successfully")
        except Exception as e:
            logger.error(f"Map retrieval error: {e}")
            raise gr.Error(f"Failed to retrieve map data: {str(e)}")

        # Create input visualization
        from maploc.utils.viz_2d import plot_images
        plot_images([image, map_viz], titles=["input image", "OpenStreetMap raster"], pad=2)
        plot_nodes(1, canvas.raster[2], fontsize=6, size=10)
        fig1 = plt.gcf()

        # Run inference
        try:
            logger.info("Starting localization inference...")
            uv, yaw, prob, neural_map, image_rectified = demo.localize(
                image, camera, canvas, gravity=gravity
            )
            logger.info("Localization completed successfully")
        except RuntimeError as e:
            logger.error(f"Inference error: {e}")
            raise gr.Error(f"Localization failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected inference error: {e}")
            raise gr.Error(f"Unexpected error during localization: {str(e)}")

        # Create output visualizations
        try:
            from maploc.utils.viz_localization import (
                add_circle_inset,
                likelihood_overlay,
                plot_dense_rotations,
            )
            from maploc.utils.viz_2d import features_to_RGB
            
            overlay = likelihood_overlay(prob.numpy().max(-1), map_viz.mean(-1, keepdims=True))
            (neural_map_rgb,) = features_to_RGB(neural_map.numpy())
            plot_images([overlay, neural_map_rgb], titles=["heatmap", "neural map"], pad=2)
            ax = plt.gcf().axes[0]
            ax.scatter(*canvas.to_uv(bbox.center), s=5, c="red")
            plot_dense_rotations(ax, prob, w=0.005, s=1 / 25)
            add_circle_inset(ax, uv)
            fig2 = plt.gcf()

            # Create interactive map
            latlon = proj.unproject(canvas.to_xy(uv))
            bbox_latlon = proj.unproject(canvas.bbox)
            plot = GeoPlotter(zoom=16.5)
            plot.raster(map_viz, bbox_latlon, opacity=0.5)
            plot.raster(likelihood_overlay(prob.numpy().max(-1)), proj.unproject(bbox))
            plot.points(proj.latlonalt[:2], "red", name="location prior", size=10)
            plot.points(latlon, "black", name="argmax", size=10, visible="legendonly")
            plot.bbox(bbox_latlon, "blue", name="map tile")

            coordinates = f"(latitude, longitude) = {tuple(map(float, latlon))}"
            coordinates += f"\nheading angle = {yaw:.2f}°"
            
            logger.info(f"Localization result: {coordinates}")
            return fig1, fig2, plot.fig, coordinates
            
        except Exception as e:
            logger.error(f"Visualization error: {e}")
            raise gr.Error(f"Failed to create visualizations: {str(e)}")

    except Exception as e:
        logger.error(f"Unexpected error in run function: {e}")
        raise gr.Error(f"Unexpected error: {str(e)}")

# Example images
examples = [
    ["assets/query_zurich_1.JPG", "ETH CAB Zurich", 128, 256],
    ["assets/query_vancouver_1.JPG", "Vancouver Waterfront Station", 128, 256],
    ["assets/query_vancouver_2.JPG", None, 128, 256],
    ["assets/query_vancouver_3.JPG", None, 128, 256],
]

description = """
<h1 align="center">
  <ins>OrienterNet</ins> - Docker Demo (Simplified)
  <br>
  Visual Localization in 2D Public Maps
  <br>
  with Neural Matching</h1>
<h3 align="center">
    <a href="https://psarlin.com/orienternet" target="_blank">Project Page</a> |
    <a href="https://arxiv.org/pdf/2304.02009.pdf" target="_blank">Paper</a> |
    <a href="https://github.com/facebookresearch/OrienterNet" target="_blank">Code</a> |
    <a href="https://youtu.be/wglW8jnupSs" target="_blank">Video</a>
</h3>
<p align="center">
🐳 <strong>Running in Docker Container (Simplified Version)</strong> 🐳<br>
OrienterNet finds the position and orientation of any image using OpenStreetMap.<br>
Click on one of the provided examples or upload your own image!<br>
<em>Note: First run may take longer as the pretrained model downloads automatically.</em>
</p>
"""

# Create Gradio interface
app = gr.Interface(
    fn=run,
    inputs=[
        gr.File(file_types=["image"], label="Upload Image"),
        gr.Textbox(
            label="Prior location (optional)",
            info="Required if the image metadata (EXIF) does not contain a GPS prior. "
            "Enter an address or a street or building name.",
            placeholder="e.g., Times Square New York, Eiffel Tower Paris"
        ),
        gr.Radio(
            [64, 128, 256, 512],
            value=128,
            label="Search radius (meters)",
            info="Depends on how coarse the prior location is.",
        ),
        gr.Radio(
            [64, 128, 256, 360],
            value=256,
            label="Number of rotations",
            info="Reduce to scale to larger areas. Higher = more accurate but slower.",
        ),
    ],
    outputs=[
        gr.Plot(label="📷 Inputs (Image + Map)"),
        gr.Plot(label="🎯 AI Predictions (Heatmap + Neural Map)"),
        gr.Plot(label="🗺️ Interactive Map"),
        gr.Textbox(label="📍 Predicted Coordinates"),
    ],
    title="OrienterNet Docker Demo (Simplified)",
    description=description,
    examples=examples,
    cache_examples=False,  # Disable caching in Docker
    allow_flagging="never",
)

if __name__ == "__main__":
    logger.info("Starting OrienterNet Docker Demo (Simplified)...")
    logger.info("The pretrained model will be downloaded automatically on first use.")
    
    # Launch with Docker-friendly settings
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True,
        quiet=False
    )
